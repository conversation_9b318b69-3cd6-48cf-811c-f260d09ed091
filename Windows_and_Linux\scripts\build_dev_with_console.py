#!/usr/bin/env python3
"""
Simple build dev script that always enables console mode
This allows you to see real-time logs when the application runs
"""

import os
import subprocess
import sys
from pathlib import Path

# Configuration
DEFAULT_VENV_NAME = "myvenv"
DEFAULT_SCRIPT_NAME = "main.py"
MODE = "build-dev"

# Import utilities based on platform
if os.name == "nt":  # Windows
    from utils import (
        check_data,
        clear_console,
        copy_required_files,
        get_activation_script,
        get_executable_name,
        get_project_root,
        setup_environment,
        terminate_existing_processes,
    )
else:  # Linux/Unix
    from .utils import (
        check_data,
        clear_console,
        copy_required_files,
        get_activation_script,
        get_executable_name,
        get_project_root,
        setup_environment,
        terminate_existing_processes,
    )


def copy_required_files_dev():
    """Copy required files for the development build to dist/dev/."""
    return copy_required_files("development", "dev")


def main():
    """Main build function - always with console enabled"""
    print("===== Writing Tools - Development Build (Console Mode) =====")
    
    try:
        # Setup project root
        project_root = get_project_root()
        print(f"Project root: {project_root.name}")

        # Setup environment (virtual env + dependencies)
        print("Setting up development environment...")
        success, _ = setup_environment(DEFAULT_VENV_NAME)
        if not success:
            print("\nFailed to setup environment!")
            return 1

        # Copy required files
        print("Copying required files for development build...")
        if not copy_required_files_dev():
            print("Failed to copy required files!")
            return 1

        # Terminate existing processes
        print("Terminating existing processes...")
        terminate_existing_processes()

        # Setup development settings
        print("Setting up development settings...")
        check_data(MODE)

        # Remove existing spec file to force console mode
        spec_file = Path("Writing Tools.spec")
        if spec_file.exists():
            spec_file.unlink()
            print("Removed existing Writing Tools.spec to regenerate with console mode")

        # Build with PyInstaller (console mode)
        print("Starting PyInstaller development build (console mode)...")
        
        # Get Python executable and activation script
        python_exe = get_executable_name()
        activation_script = get_activation_script(DEFAULT_VENV_NAME)
        
        # PyInstaller command with console enabled
        pyinstaller_cmd = [
            python_exe, "-m", "PyInstaller",
            "--onedir",
            "--console",  # Force console mode
            "--name", "Writing Tools",
            "--distpath", "./dist/dev",
            "--workpath", "./build",
            "--specpath", ".",
            "--icon", "./config/icons/icon.ico",
            "--add-data", "./config/icons;icons",
            "--add-data", "./config/backgrounds/background.png;.",
            "--add-data", "./config/backgrounds/background_dark.png;.",
            "--add-data", "./config/backgrounds/background_popup.png;.",
            "--add-data", "./config/backgrounds/background_popup_dark.png;.",
            "--hidden-import", "pynput.keyboard._win32",
            "--hidden-import", "pynput.mouse._win32",
            "--collect-all", "darkdetect",
            DEFAULT_SCRIPT_NAME
        ]

        # Execute build command
        if activation_script:
            full_cmd = f'"{activation_script}" && {" ".join(pyinstaller_cmd)}'
            result = subprocess.run(full_cmd, shell=True, check=True)
        else:
            result = subprocess.run(pyinstaller_cmd, check=True)

        print("PyInstaller development build completed successfully (console mode)!")
        print("Console mode enabled - logs will be visible in terminal when running the exe")

        # Launch the built executable
        exe_path = Path("dist/dev/Writing Tools.exe")
        if exe_path.exists():
            print(f"\nLaunching {exe_path} with console mode...")
            print("\n" + "="*50)
            print("=== Writing Tools - Console Mode ===")
            print("Logs will appear in this console window.")
            print("Press Ctrl+C to exit.")
            print("="*50)
            
            # Launch the executable and keep console open
            subprocess.run([str(exe_path.absolute())])
        else:
            print(f"Warning: Executable not found at {exe_path}")

        print("\n===== Development build completed =====")
        print("The executable and required files are in the 'dist/dev' directory.")
        print("Console mode was enabled - you can see logs directly in the terminal when the exe runs.")

        return 0

    except subprocess.CalledProcessError as e:
        print(f"Build failed: {e}")
        return 1
    except KeyboardInterrupt:
        print("\nBuild cancelled by user")
        return 1
    except Exception as e:
        print(f"Unexpected error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
