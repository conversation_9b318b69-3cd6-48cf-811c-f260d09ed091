#!/usr/bin/env python3
"""
Script simple pour build dev avec console et test
Usage: python scripts/simple_console_build_test.py
"""

import os
import subprocess
import sys
import time
from pathlib import Path


def print_header(title):
    """Affiche un en-tête formaté"""
    print("\n" + "=" * 60)
    print(f"  {title}")
    print("=" * 60)


def print_step(step_num, description):
    """Affiche une étape numérotée"""
    print(f"\n[{step_num}] {description}")
    print("-" * 40)


def check_prerequisites():
    """Vérifie les prérequis"""
    print_step(1, "Vérification des prérequis")

    # Vérifier Python
    python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
    print(f"✅ Python version: {python_version}")

    # Vérifier le répertoire de travail
    current_dir = Path.cwd()
    expected_dir = Path(__file__).parent.parent  # Windows_and_Linux

    if current_dir != expected_dir:
        print(f"📁 Changement de répertoire: {expected_dir}")
        os.chdir(expected_dir)
    else:
        print(f"✅ Répertoire de travail: {current_dir}")

    # Vérifier que build_dev.py existe
    build_script = Path("scripts/build_dev.py")
    if build_script.exists():
        print(f"✅ Script de build trouvé: {build_script}")
    else:
        print(f"❌ Script de build non trouvé: {build_script}")
        return False

    return True


def clean_previous_build():
    """Nettoie les builds précédents"""
    print_step(2, "Nettoyage des builds précédents")

    dist_dir = Path("dist/dev")
    if dist_dir.exists():
        print(f"🧹 Suppression du répertoire: {dist_dir}")
        import shutil

        try:
            shutil.rmtree(dist_dir)
            print("✅ Nettoyage terminé")
        except Exception as e:
            print(f"⚠️ Erreur lors du nettoyage: {e}")
    else:
        print("✅ Aucun build précédent à nettoyer")


def build_with_console():
    """Lance le build avec mode console"""
    print_step(3, "Build en mode console")

    try:
        print("🔨 Lancement du build...")
        start_time = time.time()

        # Lancer le build avec console
        result = subprocess.run(
            [sys.executable, "scripts/build_dev.py", "--console"],
            check=True,
            capture_output=False,  # Afficher la sortie en temps réel
            text=True,
        )

        end_time = time.time()
        duration = end_time - start_time

        print(f"\n✅ Build terminé avec succès en {duration:.1f} secondes")
        return True

    except subprocess.CalledProcessError as e:
        print(f"\n❌ Erreur lors du build: {e}")
        return False
    except KeyboardInterrupt:
        print("\n⚠️ Build interrompu par l'utilisateur")
        return False


def verify_build():
    """Vérifie que le build a réussi"""
    print_step(4, "Vérification du build")

    exe_path = Path("dist/dev/Writing Tools.exe")

    if exe_path.exists():
        file_size = exe_path.stat().st_size / (1024 * 1024)  # MB
        print(f"✅ Exécutable créé: {exe_path}")
        print(f"📊 Taille du fichier: {file_size:.1f} MB")

        # Vérifier les fichiers associés
        dist_files = list(Path("dist/dev").glob("*"))
        print(f"📁 Fichiers dans dist/dev: {len(dist_files)} fichiers")

        return True
    else:
        print(f"❌ Exécutable non trouvé: {exe_path}")

        # Lister ce qui existe dans dist/
        dist_dir = Path("dist")
        if dist_dir.exists():
            print("📁 Contenu du répertoire dist/:")
            for item in dist_dir.rglob("*"):
                if item.is_file():
                    print(f"  - {item}")

        return False


def test_executable():
    """Teste l'exécutable (optionnel)"""
    print_step(5, "Test de l'exécutable (optionnel)")

    exe_path = Path("dist/dev/Writing Tools.exe")

    if not exe_path.exists():
        print("❌ Impossible de tester: exécutable non trouvé")
        return False

    print("ℹ️ Test manuel recommandé:")
    print(f"   1. Ouvrir: {exe_path.absolute()}")
    print("   2. Vérifier que la console s'affiche")
    print("   3. Vérifier les logs en temps réel")
    print("   4. Tester une fonctionnalité de base")

    # Demander si l'utilisateur veut lancer automatiquement
    try:
        response = input("\n🤔 Voulez-vous lancer l'exécutable maintenant? (y/N): ").strip().lower()
        if response in ["y", "yes", "oui", "o"]:
            print("🚀 Lancement de l'exécutable...")
            subprocess.Popen([str(exe_path.absolute())])
            print("✅ Exécutable lancé (vérifiez la fenêtre qui s'ouvre)")
            return True
    except KeyboardInterrupt:
        print("\n⚠️ Test annulé")

    return True


def main():
    """Fonction principale"""
    print_header("BUILD DEV CONSOLE - SCRIPT SIMPLE")
    print("Ce script va:")
    print("  1. Vérifier les prérequis")
    print("  2. Nettoyer les builds précédents")
    print("  3. Lancer le build en mode console")
    print("  4. Vérifier le résultat")
    print("  5. Proposer de tester l'exécutable")

    try:
        # Étape 1: Prérequis
        if not check_prerequisites():
            print("\n❌ Prérequis non satisfaits")
            return 1

        # Étape 2: Nettoyage
        clean_previous_build()

        # Étape 3: Build
        if not build_with_console():
            print("\n❌ Échec du build")
            return 1

        # Étape 4: Vérification
        if not verify_build():
            print("\n❌ Build incomplet")
            return 1

        # Étape 5: Test
        test_executable()

        # Résumé final
        print_header("RÉSUMÉ")
        print("✅ Build dev avec console terminé avec succès!")
        print("✅ Exécutable prêt à être testé")
        print(f"📁 Emplacement: {Path('dist/dev/Writing Tools.exe').absolute()}")

        return 0

    except KeyboardInterrupt:
        print("\n\n⚠️ Script interrompu par l'utilisateur")
        return 1
    except Exception as e:
        print(f"\n❌ Erreur inattendue: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
